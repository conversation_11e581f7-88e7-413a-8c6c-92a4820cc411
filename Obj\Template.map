Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.GetDistortion) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.GetDistortion) refers to fflti.o(.text) for __aeabi_i2f
    main.o(i.GetDistortion) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.GetDistortion) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.GetDistortion) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.GetDistortion) refers to sqrt.o(i.sqrt) for sqrt
    main.o(i.GetDistortion) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.GetDistortion) refers to fdiv.o(.text) for __aeabi_fdiv
    main.o(i.GetDistortion) refers to main.o(.data) for Max_Val
    main.o(i.GetDistortion) refers to fft_calculate.o(.bss) for MagBufArray
    main.o(i.main) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to ad9833.o(i.AD9833_Init_GPIO) for AD9833_Init_GPIO
    main.o(i.main) refers to ad9833_2.o(i.AD9833_2_Init_GPIO) for AD9833_2_Init_GPIO
    main.o(i.main) refers to usart.o(i.USART1_Init) for USART1_Init
    main.o(i.main) refers to adc.o(i.TIM1_Init) for TIM1_Init
    main.o(i.main) refers to adc.o(i.ADC1_DMA1_Init) for ADC1_DMA1_Init
    main.o(i.main) refers to adc.o(i.ADC1_Init) for ADC1_Init
    main.o(i.main) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    main.o(i.main) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    main.o(i.main) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.main) refers to dflti.o(.text) for __aeabi_i2d
    main.o(i.main) refers to ad9833.o(i.AD9833_WaveSeting) for AD9833_WaveSeting
    main.o(i.main) refers to ad9833_2.o(i.AD9833_2_WaveSeting) for AD9833_2_WaveSeting
    main.o(i.main) refers to ad9833.o(i.AD9833_AmpSet) for AD9833_AmpSet
    main.o(i.main) refers to ad9833_2.o(i.AD9833_2_AmpSet) for AD9833_2_AmpSet
    main.o(i.main) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.main) refers to main.o(.data) for run_flag
    main.o(i.main) refers to fft_calculate.o(.bss) for MagBufArray
    main.o(i.main) refers to main.o(.bss) for txt
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    timer.o(i.TIM_ITSetUp) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    timer.o(i.TIM_ITSetUp) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.TIM_Init_counter) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM_Init_counter) refers to stm32f10x_tim.o(i.TIM_DeInit) for TIM_DeInit
    timer.o(i.TIM_Init_counter) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    timer.o(i.TIM_Init_counter) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM_Init_counter) refers to stm32f10x_tim.o(i.TIM_ETRClockMode2Config) for TIM_ETRClockMode2Config
    timer.o(i.TIM_Init_counter) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    timer.o(i.TIM_Init_timer) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM_Init_timer) refers to stm32f10x_tim.o(i.TIM_DeInit) for TIM_DeInit
    timer.o(i.TIM_Init_timer) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    timer.o(i.TIM_Init_timer) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM_Init_timer) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    timer.o(i.TIM_Init_timer) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM_Init_timer) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.calculer) refers to ffltui.o(.text) for __aeabi_ui2f
    timer.o(i.calculer) refers to fdiv.o(.text) for __aeabi_fdiv
    timer.o(i.calculer) refers to fmul.o(.text) for __aeabi_fmul
    timer.o(i.calculer) refers to ffixui.o(.text) for __aeabi_f2uiz
    ad9833.o(i.AD9833_AmpSet) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9833.o(i.AD9833_AmpSet) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9833.o(i.AD9833_AmpSet) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833.o(i.AD9833_Init_GPIO) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ad9833.o(i.AD9833_Init_GPIO) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9833.o(i.AD9833_WaveSeting) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_WaveSeting) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_WaveSeting) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_WaveSeting) refers to ad9833.o(i.AD9833_Write) for AD9833_Write
    ad9833.o(i.AD9833_Write) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9833.o(i.AD9833_Write) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9833.o(i.AD9833_Write) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833_2.o(i.AD9833_2_AmpSet) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9833_2.o(i.AD9833_2_AmpSet) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9833_2.o(i.AD9833_2_AmpSet) refers to ad9833_2.o(i.AD9833_2_Delay) for AD9833_2_Delay
    ad9833_2.o(i.AD9833_2_Init_GPIO) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ad9833_2.o(i.AD9833_2_Init_GPIO) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9833_2.o(i.AD9833_2_WaveSeting) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833_2.o(i.AD9833_2_WaveSeting) refers to dmul.o(.text) for __aeabi_dmul
    ad9833_2.o(i.AD9833_2_WaveSeting) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833_2.o(i.AD9833_2_WaveSeting) refers to ad9833_2.o(i.AD9833_Write_2) for AD9833_Write_2
    ad9833_2.o(i.AD9833_Write_2) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9833_2.o(i.AD9833_Write_2) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9833_2.o(i.AD9833_Write_2) refers to ad9833_2.o(i.AD9833_2_Delay) for AD9833_2_Delay
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to adc.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    tftlcd.o(i.LCD_Clear) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_Clear) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_Clear) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_Display_Dir) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_Display_Dir) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.LCD_Display_Dir) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_DrawFRONT_COLOR) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_DrawFRONT_COLOR) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_DrawLine) refers to tftlcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    tftlcd.o(i.LCD_DrawLine_Color) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    tftlcd.o(i.LCD_DrawPoint) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_DrawPoint) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_DrawPoint) refers to tftlcd.o(.data) for FRONT_COLOR
    tftlcd.o(i.LCD_DrawRectangle) refers to tftlcd.o(i.LCD_DrawLine) for LCD_DrawLine
    tftlcd.o(i.LCD_Draw_Circle) refers to tftlcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    tftlcd.o(i.LCD_DrowSign) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_DrowSign) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_Fill) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_Fill) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(i.LCD_ReadData) for LCD_ReadData
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_Set_Window) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_Set_Window) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.LCD_Set_Window) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_ShowChar) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    tftlcd.o(i.LCD_ShowChar) refers to tftlcd.o(.constdata) for ascii_1206
    tftlcd.o(i.LCD_ShowChar) refers to tftlcd.o(.data) for FRONT_COLOR
    tftlcd.o(i.LCD_ShowFontHZ) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    tftlcd.o(i.LCD_ShowFontHZ) refers to tftlcd.o(.constdata) for CnChar32x29
    tftlcd.o(i.LCD_ShowFontHZ) refers to tftlcd.o(.data) for FRONT_COLOR
    tftlcd.o(i.LCD_ShowNum) refers to tftlcd.o(i.LCD_Pow) for LCD_Pow
    tftlcd.o(i.LCD_ShowNum) refers to tftlcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tftlcd.o(i.LCD_ShowPicture) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_ShowPicture) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_ShowString) refers to tftlcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tftlcd.o(i.LCD_ShowxNum) refers to tftlcd.o(i.LCD_Pow) for LCD_Pow
    tftlcd.o(i.LCD_ShowxNum) refers to tftlcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tftlcd.o(i.LCD_WriteCmdData) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_WriteCmdData) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.TFTLCD_FSMC_Init) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    tftlcd.o(i.TFTLCD_FSMC_Init) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    tftlcd.o(i.TFTLCD_FSMC_Init) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    tftlcd.o(i.TFTLCD_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tftlcd.o(i.TFTLCD_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.TFTLCD_GPIO_Init) for TFTLCD_GPIO_Init
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.TFTLCD_FSMC_Init) for TFTLCD_FSMC_Init
    tftlcd.o(i.TFTLCD_Init) refers to systick.o(i.delay_ms) for delay_ms
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_ReadData) for LCD_ReadData
    tftlcd.o(i.TFTLCD_Init) refers to printfa.o(i.__0printf) for __2printf
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_Clear) for LCD_Clear
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(.data) for tftlcd_data
    adc.o(i.ADC1_DMA1_Init) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    adc.o(i.ADC1_DMA1_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    adc.o(i.ADC1_DMA1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    adc.o(i.ADC1_DMA1_Init) refers to stm32f10x_dma.o(i.DMA_DeInit) for DMA_DeInit
    adc.o(i.ADC1_DMA1_Init) refers to stm32f10x_dma.o(i.DMA_Init) for DMA_Init
    adc.o(i.ADC1_DMA1_Init) refers to stm32f10x_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    adc.o(i.ADC1_DMA1_Init) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc.o(i.ADC1_DMA1_Init) refers to adc.o(.bss) for ADC_Value
    adc.o(i.ADC1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.ADC1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd) for ADC_ExternalTrigConvCmd
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.DMA1_Channel1_IRQHandler) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc.o(i.DMA1_Channel1_IRQHandler) refers to stm32f10x_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA1_Channel1_IRQHandler) refers to cr4_fft_256_stm32.o(.text) for cr4_fft_256_stm32
    adc.o(i.DMA1_Channel1_IRQHandler) refers to fft_calculate.o(i.GetPowerMag) for GetPowerMag
    adc.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(.bss) for ADC_Value
    adc.o(i.DMA1_Channel1_IRQHandler) refers to fft_calculate.o(.bss) for InBufArray
    adc.o(i.DMA1_Channel1_IRQHandler) refers to main.o(.data) for run_flag
    adc.o(i.TIM1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.TIM1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    adc.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    adc.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    adc.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    adc.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    systick.o(i.SysTick_Init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    systick.o(i.SysTick_Init) refers to systick.o(.data) for fac_us
    systick.o(i.delay_ms) refers to systick.o(.data) for fac_ms
    systick.o(i.delay_us) refers to systick.o(.data) for fac_us
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart.o(i.USART1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.USART1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    fft_calculate.o(i.GetPowerMag) refers to fflti.o(.text) for __aeabi_i2f
    fft_calculate.o(i.GetPowerMag) refers to fmul.o(.text) for __aeabi_fmul
    fft_calculate.o(i.GetPowerMag) refers to fdiv.o(.text) for __aeabi_fdiv
    fft_calculate.o(i.GetPowerMag) refers to fadd.o(.text) for __aeabi_fadd
    fft_calculate.o(i.GetPowerMag) refers to f2d.o(.text) for __aeabi_f2d
    fft_calculate.o(i.GetPowerMag) refers to sqrt.o(i.sqrt) for sqrt
    fft_calculate.o(i.GetPowerMag) refers to ddiv.o(.text) for __aeabi_ddiv
    fft_calculate.o(i.GetPowerMag) refers to d2f.o(.text) for __aeabi_d2f
    fft_calculate.o(i.GetPowerMag) refers to atan2.o(i.atan2) for atan2
    fft_calculate.o(i.GetPowerMag) refers to dmul.o(.text) for __aeabi_dmul
    fft_calculate.o(i.GetPowerMag) refers to dfixi.o(.text) for __aeabi_d2iz
    fft_calculate.o(i.GetPowerMag) refers to ffixui.o(.text) for __aeabi_f2uiz
    fft_calculate.o(i.GetPowerMag) refers to fft_calculate.o(.bss) for OutBufArray
    fft_calculate.o(i.GetPowerMag) refers to fft_calculate.o(.data) for P
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(i.GetDistortion), (148 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing timer.o(i.TIM_ITSetUp), (76 bytes).
    Removing timer.o(i.TIM_Init_counter), (140 bytes).
    Removing timer.o(i.TIM_Init_timer), (152 bytes).
    Removing timer.o(i.calculer), (52 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing tftlcd.o(i.LCD_Clear), (72 bytes).
    Removing tftlcd.o(i.LCD_Display_Dir), (100 bytes).
    Removing tftlcd.o(i.LCD_DrawFRONT_COLOR), (28 bytes).
    Removing tftlcd.o(i.LCD_DrawLine), (176 bytes).
    Removing tftlcd.o(i.LCD_DrawLine_Color), (172 bytes).
    Removing tftlcd.o(i.LCD_DrawPoint), (32 bytes).
    Removing tftlcd.o(i.LCD_DrawRectangle), (60 bytes).
    Removing tftlcd.o(i.LCD_Draw_Circle), (152 bytes).
    Removing tftlcd.o(i.LCD_DrowSign), (132 bytes).
    Removing tftlcd.o(i.LCD_Fill), (96 bytes).
    Removing tftlcd.o(i.LCD_Pow), (22 bytes).
    Removing tftlcd.o(i.LCD_ReadData), (12 bytes).
    Removing tftlcd.o(i.LCD_ReadPoint), (76 bytes).
    Removing tftlcd.o(i.LCD_Set_Window), (196 bytes).
    Removing tftlcd.o(i.LCD_ShowChar), (296 bytes).
    Removing tftlcd.o(i.LCD_ShowFontHZ), (232 bytes).
    Removing tftlcd.o(i.LCD_ShowNum), (148 bytes).
    Removing tftlcd.o(i.LCD_ShowPicture), (92 bytes).
    Removing tftlcd.o(i.LCD_ShowString), (102 bytes).
    Removing tftlcd.o(i.LCD_ShowxNum), (190 bytes).
    Removing tftlcd.o(i.LCD_WriteCmd), (12 bytes).
    Removing tftlcd.o(i.LCD_WriteCmdData), (20 bytes).
    Removing tftlcd.o(i.LCD_WriteData), (12 bytes).
    Removing tftlcd.o(i.LCD_WriteData_Color), (12 bytes).
    Removing tftlcd.o(i.TFTLCD_FSMC_Init), (124 bytes).
    Removing tftlcd.o(i.TFTLCD_GPIO_Init), (112 bytes).
    Removing tftlcd.o(i.TFTLCD_Init), (692 bytes).
    Removing tftlcd.o(.constdata), (7142 bytes).
    Removing tftlcd.o(.data), (12 bytes).
    Removing adc.o(.data), (32 bytes).
    Removing systick.o(i.delay_ms), (76 bytes).
    Removing systick.o(i.delay_us), (76 bytes).
    Removing usart.o(i.fputc), (36 bytes).
    Removing cr4_fft_1024_stm32.o(.text), (4576 bytes).
    Removing ffltui.o(.text), (10 bytes).
    Removing cdcmple.o(.text), (48 bytes).

347 unused section(s) (total 29208 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    APP\adc\adc.c                            0x00000000   Number         0  adc.o ABSOLUTE
    APP\led\led.c                            0x00000000   Number         0  led.o ABSOLUTE
    APP\tftlcd\tftlcd.c                      0x00000000   Number         0  tftlcd.o ABSOLUTE
    DSP_Library\Src\fft_calculate.c          0x00000000   Number         0  fft_calculate.o ABSOLUTE
    DSP_Library\cr4_fft_1024_stm32.s         0x00000000   Number         0  cr4_fft_1024_stm32.o ABSOLUTE
    DSP_Library\cr4_fft_256_stm32.s          0x00000000   Number         0  cr4_fft_256_stm32.o ABSOLUTE
    Libraries\CMSIS\core_cm3.c               0x00000000   Number         0  core_cm3.o ABSOLUTE
    Libraries\CMSIS\startup_stm32f10x_hd.s   0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    Libraries\CMSIS\system_stm32f10x.c       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_adc.c 0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_bkp.c 0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_can.c 0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dac.c 0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dma.c 0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_exti.c 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_iwdg.c 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_pwr.c 0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rtc.c 0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_wwdg.c 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Libraries\\CMSIS\\core_cm3.c             0x00000000   Number         0  core_cm3.o ABSOLUTE
    Public\SysTick.c                         0x00000000   Number         0  systick.o ABSOLUTE
    Public\system.c                          0x00000000   Number         0  system.o ABSOLUTE
    Public\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    User\AD9833.c                            0x00000000   Number         0  ad9833.o ABSOLUTE
    User\AD9833_2.c                          0x00000000   Number         0  ad9833_2.o ABSOLUTE
    User\OLED.c                              0x00000000   Number         0  oled.o ABSOLUTE
    User\Timer.c                             0x00000000   Number         0  timer.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section     1502  cr4_fft_256_stm32.o(.text)
    .text                                    0x08000746   Section        0  fadd.o(.text)
    .text                                    0x080007f6   Section        0  fmul.o(.text)
    .text                                    0x0800085a   Section        0  fdiv.o(.text)
    .text                                    0x080008d6   Section        0  dmul.o(.text)
    .text                                    0x080009ba   Section        0  ddiv.o(.text)
    .text                                    0x08000a98   Section        0  fflti.o(.text)
    .text                                    0x08000aaa   Section        0  dflti.o(.text)
    .text                                    0x08000acc   Section        0  ffixui.o(.text)
    .text                                    0x08000af4   Section        0  dfixi.o(.text)
    .text                                    0x08000b32   Section        0  f2d.o(.text)
    .text                                    0x08000b58   Section        0  d2f.o(.text)
    .text                                    0x08000b90   Section        0  uidiv.o(.text)
    .text                                    0x08000bbc   Section        0  uldiv.o(.text)
    .text                                    0x08000c1e   Section        0  llushr.o(.text)
    .text                                    0x08000c3e   Section        0  iusefp.o(.text)
    .text                                    0x08000c3e   Section        0  fepilogue.o(.text)
    .text                                    0x08000cac   Section        0  depilogue.o(.text)
    .text                                    0x08000d66   Section        0  dadd.o(.text)
    .text                                    0x08000eb4   Section        0  dsqrt.o(.text)
    .text                                    0x08000f56   Section        0  dfixul.o(.text)
    .text                                    0x08000f88   Section       48  cdrcmple.o(.text)
    .text                                    0x08000fb8   Section       36  init.o(.text)
    .text                                    0x08000fdc   Section        0  llshl.o(.text)
    .text                                    0x08000ffa   Section        0  llsshr.o(.text)
    .text                                    0x0800101e   Section        0  dscalb.o(.text)
    i.AD9833_2_AmpSet                        0x0800104c   Section        0  ad9833_2.o(i.AD9833_2_AmpSet)
    i.AD9833_2_Delay                         0x080010b0   Section        0  ad9833_2.o(i.AD9833_2_Delay)
    AD9833_2_Delay                           0x080010b1   Thumb Code    14  ad9833_2.o(i.AD9833_2_Delay)
    i.AD9833_2_Init_GPIO                     0x080010c0   Section        0  ad9833_2.o(i.AD9833_2_Init_GPIO)
    i.AD9833_2_WaveSeting                    0x080010ec   Section        0  ad9833_2.o(i.AD9833_2_WaveSeting)
    i.AD9833_AmpSet                          0x080011cc   Section        0  ad9833.o(i.AD9833_AmpSet)
    i.AD9833_Delay                           0x08001230   Section        0  ad9833.o(i.AD9833_Delay)
    AD9833_Delay                             0x08001231   Thumb Code    14  ad9833.o(i.AD9833_Delay)
    i.AD9833_Init_GPIO                       0x08001240   Section        0  ad9833.o(i.AD9833_Init_GPIO)
    i.AD9833_WaveSeting                      0x0800126c   Section        0  ad9833.o(i.AD9833_WaveSeting)
    i.AD9833_Write                           0x0800134c   Section        0  ad9833.o(i.AD9833_Write)
    i.AD9833_Write_2                         0x080013c4   Section        0  ad9833_2.o(i.AD9833_Write_2)
    i.ADC1_DMA1_Init                         0x0800143c   Section        0  adc.o(i.ADC1_DMA1_Init)
    i.ADC1_Init                              0x080014cc   Section        0  adc.o(i.ADC1_Init)
    i.ADC_Cmd                                0x0800158c   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_DMACmd                             0x080015a2   Section        0  stm32f10x_adc.o(i.ADC_DMACmd)
    i.ADC_ExternalTrigConvCmd                0x080015b8   Section        0  stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd)
    i.ADC_GetCalibrationStatus               0x080015ce   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetResetCalibrationStatus          0x080015e2   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x080015f8   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x08001648   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x08001700   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_StartCalibration                   0x0800170a   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.BusFault_Handler                       0x08001714   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DMA1_Channel1_IRQHandler               0x08001718   Section        0  adc.o(i.DMA1_Channel1_IRQHandler)
    i.DMA_ClearITPendingBit                  0x08001784   Section        0  stm32f10x_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x080017a0   Section        0  stm32f10x_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x080017b8   Section        0  stm32f10x_dma.o(i.DMA_DeInit)
    i.DMA_ITConfig                           0x08001904   Section        0  stm32f10x_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08001916   Section        0  stm32f10x_dma.o(i.DMA_Init)
    i.DebugMon_Handler                       0x08001952   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08001954   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08001a6a   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08001a7c   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08001a80   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x08001a84   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.GetPowerMag                            0x08001a90   Section        0  fft_calculate.o(i.GetPowerMag)
    i.HardFault_Handler                      0x08001bc0   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.LED_Init                               0x08001bc4   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x08001c20   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001c24   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001c28   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001c98   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08001cac   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x08001cd8   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08001d28   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08001d7c   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08001db0   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08001dd8   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x08001e86   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08001ea8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x08001f1c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08001f44   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08001f64   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08001f84   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_AHBPeriphClockCmd                  0x08001f88   Section        0  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001fa8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001fc8   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x0800209c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x0800209e   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x0800209f   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080020a8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080020a9   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x08002188   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x080021b0   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SysTick_Init                           0x080021b4   Section        0  systick.o(i.SysTick_Init)
    i.SystemInit                             0x080021f0   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM1_Init                              0x08002250   Section        0  adc.o(i.TIM1_Init)
    i.TIM_Cmd                                0x080022e0   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x080022f8   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_OC1Init                            0x08002318   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_TimeBaseInit                       0x080023b0   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.TIM_TimeBaseStructInit                 0x08002454   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseStructInit)
    i.USART1_IRQHandler                      0x08002468   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART1_Init                            0x080024a4   Section        0  usart.o(i.USART1_Init)
    i.USART_ClearFlag                        0x0800255c   Section        0  stm32f10x_usart.o(i.USART_ClearFlag)
    i.USART_Cmd                              0x0800256e   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08002586   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x080025a0   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080025f4   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08002640   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08002718   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08002722   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x0800272a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__0sprintf                             0x08002730   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_fpclassify                       0x08002758   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x08002780   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x0800282a   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08002830   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_underflow                0x08002834   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08002844   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002852   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002854   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08002864   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08002870   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08002871   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080029f4   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080029f5   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080030d0   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080030d1   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080030f4   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080030f5   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08003122   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08003123   Thumb Code    10  printfa.o(i._sputc)
    i.atan                                   0x0800312c   Section        0  atan.o(i.atan)
    i.atan2                                  0x0800334c   Section        0  atan2.o(i.atan2)
    i.main                                   0x080034cc   Section        0  main.o(i.main)
    i.sqrt                                   0x0800374c   Section        0  sqrt.o(i.sqrt)
    .constdata                               0x08003798   Section     1520  oled.o(.constdata)
    .constdata                               0x08003d88   Section      152  atan.o(.constdata)
    atanhi                                   0x08003d88   Data          32  atan.o(.constdata)
    atanlo                                   0x08003da8   Data          32  atan.o(.constdata)
    aTodd                                    0x08003dc8   Data          40  atan.o(.constdata)
    aTeven                                   0x08003df0   Data          48  atan.o(.constdata)
    .constdata                               0x08003e20   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section       52  main.o(.data)
    .data                                    0x20000034   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000034   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000044   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000048   Section        4  systick.o(.data)
    fac_us                                   0x20000048   Data           1  systick.o(.data)
    fac_ms                                   0x2000004a   Data           2  systick.o(.data)
    .data                                    0x2000004c   Section        4  fft_calculate.o(.data)
    .data                                    0x20000050   Section        4  errno.o(.data)
    _errno                                   0x20000050   Data           4  errno.o(.data)
    .bss                                     0x20000054   Section       60  main.o(.bss)
    .bss                                     0x20000090   Section     1024  adc.o(.bss)
    .bss                                     0x20000490   Section     2048  fft_calculate.o(.bss)
    STACK                                    0x20000c90   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    cr4_fft_256_stm32                        0x08000169   Thumb Code     0  cr4_fft_256_stm32.o(.text)
    __aeabi_fadd                             0x08000747   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x080007eb   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x080007f1   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x080007f7   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x0800085b   Thumb Code   124  fdiv.o(.text)
    __aeabi_dmul                             0x080008d7   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080009bb   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2f                              0x08000a99   Thumb Code    18  fflti.o(.text)
    __aeabi_i2d                              0x08000aab   Thumb Code    34  dflti.o(.text)
    __aeabi_f2uiz                            0x08000acd   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2iz                             0x08000af5   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x08000b33   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000b59   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000b91   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000b91   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000bbd   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsr                             0x08000c1f   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000c1f   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000c3f   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000c3f   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000c51   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000cad   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000ccb   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000d67   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000ea9   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000eaf   Thumb Code     6  dadd.o(.text)
    _dsqrt                                   0x08000eb5   Thumb Code   162  dsqrt.o(.text)
    __aeabi_d2ulz                            0x08000f57   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000f89   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000fb9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000fb9   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000fdd   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000fdd   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000ffb   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000ffb   Thumb Code     0  llsshr.o(.text)
    __ARM_scalbn                             0x0800101f   Thumb Code    46  dscalb.o(.text)
    scalbn                                   0x0800101f   Thumb Code     0  dscalb.o(.text)
    AD9833_2_AmpSet                          0x0800104d   Thumb Code    96  ad9833_2.o(i.AD9833_2_AmpSet)
    AD9833_2_Init_GPIO                       0x080010c1   Thumb Code    40  ad9833_2.o(i.AD9833_2_Init_GPIO)
    AD9833_2_WaveSeting                      0x080010ed   Thumb Code   214  ad9833_2.o(i.AD9833_2_WaveSeting)
    AD9833_AmpSet                            0x080011cd   Thumb Code    96  ad9833.o(i.AD9833_AmpSet)
    AD9833_Init_GPIO                         0x08001241   Thumb Code    40  ad9833.o(i.AD9833_Init_GPIO)
    AD9833_WaveSeting                        0x0800126d   Thumb Code   214  ad9833.o(i.AD9833_WaveSeting)
    AD9833_Write                             0x0800134d   Thumb Code   116  ad9833.o(i.AD9833_Write)
    AD9833_Write_2                           0x080013c5   Thumb Code   116  ad9833_2.o(i.AD9833_Write_2)
    ADC1_DMA1_Init                           0x0800143d   Thumb Code   130  adc.o(i.ADC1_DMA1_Init)
    ADC1_Init                                0x080014cd   Thumb Code   178  adc.o(i.ADC1_Init)
    ADC_Cmd                                  0x0800158d   Thumb Code    22  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_DMACmd                               0x080015a3   Thumb Code    22  stm32f10x_adc.o(i.ADC_DMACmd)
    ADC_ExternalTrigConvCmd                  0x080015b9   Thumb Code    22  stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd)
    ADC_GetCalibrationStatus                 0x080015cf   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetResetCalibrationStatus            0x080015e3   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x080015f9   Thumb Code    70  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x08001649   Thumb Code   184  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x08001701   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_StartCalibration                     0x0800170b   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    BusFault_Handler                         0x08001715   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DMA1_Channel1_IRQHandler                 0x08001719   Thumb Code    86  adc.o(i.DMA1_Channel1_IRQHandler)
    DMA_ClearITPendingBit                    0x08001785   Thumb Code    18  stm32f10x_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x080017a1   Thumb Code    24  stm32f10x_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x080017b9   Thumb Code   324  stm32f10x_dma.o(i.DMA_DeInit)
    DMA_ITConfig                             0x08001905   Thumb Code    18  stm32f10x_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08001917   Thumb Code    60  stm32f10x_dma.o(i.DMA_Init)
    DebugMon_Handler                         0x08001953   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08001955   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08001a6b   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08001a7d   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08001a81   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x08001a85   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    GetPowerMag                              0x08001a91   Thumb Code   274  fft_calculate.o(i.GetPowerMag)
    HardFault_Handler                        0x08001bc1   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    LED_Init                                 0x08001bc5   Thumb Code    82  led.o(i.LED_Init)
    MemManage_Handler                        0x08001c21   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001c25   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001c29   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001c99   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08001cad   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x08001cd9   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08001d29   Thumb Code    80  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08001d7d   Thumb Code    48  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08001db1   Thumb Code    36  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08001dd9   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x08001e87   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08001ea9   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x08001f1d   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08001f45   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08001f65   Thumb Code    32  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08001f85   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_AHBPeriphClockCmd                    0x08001f89   Thumb Code    26  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001fa9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001fc9   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x0800209d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x08002189   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x080021b1   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SysTick_Init                             0x080021b5   Thumb Code    52  systick.o(i.SysTick_Init)
    SystemInit                               0x080021f1   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM1_Init                                0x08002251   Thumb Code   136  adc.o(i.TIM1_Init)
    TIM_Cmd                                  0x080022e1   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x080022f9   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_OC1Init                              0x08002319   Thumb Code   132  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_TimeBaseInit                         0x080023b1   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    TIM_TimeBaseStructInit                   0x08002455   Thumb Code    18  stm32f10x_tim.o(i.TIM_TimeBaseStructInit)
    USART1_IRQHandler                        0x08002469   Thumb Code    54  usart.o(i.USART1_IRQHandler)
    USART1_Init                              0x080024a5   Thumb Code   174  usart.o(i.USART1_Init)
    USART_ClearFlag                          0x0800255d   Thumb Code    18  stm32f10x_usart.o(i.USART_ClearFlag)
    USART_Cmd                                0x0800256f   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08002587   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x080025a1   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080025f5   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08002641   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08002719   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08002723   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x0800272b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __0sprintf                               0x08002731   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08002731   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08002731   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08002731   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08002731   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_fpclassify                         0x08002759   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x08002781   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x0800282b   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08002831   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_underflow                  0x08002835   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08002845   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002853   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002855   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08002865   Thumb Code     6  errno.o(i.__set_errno)
    atan                                     0x0800312d   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x0800334d   Thumb Code   346  atan2.o(i.atan2)
    main                                     0x080034cd   Thumb Code   532  main.o(i.main)
    sqrt                                     0x0800374d   Thumb Code    76  sqrt.o(i.sqrt)
    OLED_F8x16                               0x08003798   Data        1520  oled.o(.constdata)
    __mathlib_zero                           0x08003e20   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08003e28   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003e48   Number         0  anon$$obj.o(Region$$Table)
    AFreq                                    0x20000000   Data           4  main.o(.data)
    BFreq                                    0x20000004   Data           4  main.o(.data)
    big_AFreq                                0x20000008   Data           4  main.o(.data)
    big_BFreq                                0x2000000c   Data           4  main.o(.data)
    Row                                      0x20000010   Data           2  main.o(.data)
    last_Row                                 0x20000012   Data           2  main.o(.data)
    Max_Val                                  0x20000014   Data           2  main.o(.data)
    last_Max_Val                             0x20000016   Data           2  main.o(.data)
    last_data                                0x20000018   Data           2  main.o(.data)
    now_data                                 0x2000001a   Data           2  main.o(.data)
    next_data                                0x2000001c   Data           2  main.o(.data)
    run_flag                                 0x2000001e   Data           1  main.o(.data)
    Pha                                      0x20000020   Data           2  main.o(.data)
    k                                        0x20000024   Data           4  main.o(.data)
    base                                     0x20000028   Data           4  main.o(.data)
    harmonic                                 0x2000002c   Data           4  main.o(.data)
    distortion                               0x20000030   Data           4  main.o(.data)
    P                                        0x2000004c   Data           4  fft_calculate.o(.data)
    adc_value                                0x20000054   Data          10  main.o(.bss)
    txt                                      0x2000005e   Data          50  main.o(.bss)
    ADC_Value                                0x20000090   Data        1024  adc.o(.bss)
    InBufArray                               0x20000490   Data        1024  fft_calculate.o(.bss)
    OutBufArray                              0x20000890   Data         512  fft_calculate.o(.bss)
    MagBufArray                              0x20000a90   Data         512  fft_calculate.o(.bss)
    __initial_sp                             0x20001090   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003e9c, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003e48, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          453    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         2959  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         3272    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         3275    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         3277    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         3279    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         3280    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         3282    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         3284    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         3273    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          454    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x000005de   Code   RO         2918    .text               cr4_fft_256_stm32.o
    0x08000746   0x08000746   0x000000b0   Code   RO         3223    .text               mf_w.l(fadd.o)
    0x080007f6   0x080007f6   0x00000064   Code   RO         3225    .text               mf_w.l(fmul.o)
    0x0800085a   0x0800085a   0x0000007c   Code   RO         3227    .text               mf_w.l(fdiv.o)
    0x080008d6   0x080008d6   0x000000e4   Code   RO         3229    .text               mf_w.l(dmul.o)
    0x080009ba   0x080009ba   0x000000de   Code   RO         3231    .text               mf_w.l(ddiv.o)
    0x08000a98   0x08000a98   0x00000012   Code   RO         3233    .text               mf_w.l(fflti.o)
    0x08000aaa   0x08000aaa   0x00000022   Code   RO         3237    .text               mf_w.l(dflti.o)
    0x08000acc   0x08000acc   0x00000028   Code   RO         3239    .text               mf_w.l(ffixui.o)
    0x08000af4   0x08000af4   0x0000003e   Code   RO         3241    .text               mf_w.l(dfixi.o)
    0x08000b32   0x08000b32   0x00000026   Code   RO         3243    .text               mf_w.l(f2d.o)
    0x08000b58   0x08000b58   0x00000038   Code   RO         3245    .text               mf_w.l(d2f.o)
    0x08000b90   0x08000b90   0x0000002c   Code   RO         3287    .text               mc_w.l(uidiv.o)
    0x08000bbc   0x08000bbc   0x00000062   Code   RO         3289    .text               mc_w.l(uldiv.o)
    0x08000c1e   0x08000c1e   0x00000020   Code   RO         3291    .text               mc_w.l(llushr.o)
    0x08000c3e   0x08000c3e   0x00000000   Code   RO         3300    .text               mc_w.l(iusefp.o)
    0x08000c3e   0x08000c3e   0x0000006e   Code   RO         3301    .text               mf_w.l(fepilogue.o)
    0x08000cac   0x08000cac   0x000000ba   Code   RO         3303    .text               mf_w.l(depilogue.o)
    0x08000d66   0x08000d66   0x0000014e   Code   RO         3305    .text               mf_w.l(dadd.o)
    0x08000eb4   0x08000eb4   0x000000a2   Code   RO         3307    .text               mf_w.l(dsqrt.o)
    0x08000f56   0x08000f56   0x00000030   Code   RO         3309    .text               mf_w.l(dfixul.o)
    0x08000f86   0x08000f86   0x00000002   PAD
    0x08000f88   0x08000f88   0x00000030   Code   RO         3313    .text               mf_w.l(cdrcmple.o)
    0x08000fb8   0x08000fb8   0x00000024   Code   RO         3319    .text               mc_w.l(init.o)
    0x08000fdc   0x08000fdc   0x0000001e   Code   RO         3321    .text               mc_w.l(llshl.o)
    0x08000ffa   0x08000ffa   0x00000024   Code   RO         3323    .text               mc_w.l(llsshr.o)
    0x0800101e   0x0800101e   0x0000002e   Code   RO         3325    .text               mf_w.l(dscalb.o)
    0x0800104c   0x0800104c   0x00000064   Code   RO          416    i.AD9833_2_AmpSet   ad9833_2.o
    0x080010b0   0x080010b0   0x0000000e   Code   RO          417    i.AD9833_2_Delay    ad9833_2.o
    0x080010be   0x080010be   0x00000002   PAD
    0x080010c0   0x080010c0   0x0000002c   Code   RO          418    i.AD9833_2_Init_GPIO  ad9833_2.o
    0x080010ec   0x080010ec   0x000000e0   Code   RO          419    i.AD9833_2_WaveSeting  ad9833_2.o
    0x080011cc   0x080011cc   0x00000064   Code   RO          381    i.AD9833_AmpSet     ad9833.o
    0x08001230   0x08001230   0x0000000e   Code   RO          382    i.AD9833_Delay      ad9833.o
    0x0800123e   0x0800123e   0x00000002   PAD
    0x08001240   0x08001240   0x0000002c   Code   RO          383    i.AD9833_Init_GPIO  ad9833.o
    0x0800126c   0x0800126c   0x000000e0   Code   RO          384    i.AD9833_WaveSeting  ad9833.o
    0x0800134c   0x0800134c   0x00000078   Code   RO          385    i.AD9833_Write      ad9833.o
    0x080013c4   0x080013c4   0x00000078   Code   RO          420    i.AD9833_Write_2    ad9833_2.o
    0x0800143c   0x0800143c   0x00000090   Code   RO         2806    i.ADC1_DMA1_Init    adc.o
    0x080014cc   0x080014cc   0x000000c0   Code   RO         2807    i.ADC1_Init         adc.o
    0x0800158c   0x0800158c   0x00000016   Code   RO         1831    i.ADC_Cmd           stm32f10x_adc.o
    0x080015a2   0x080015a2   0x00000016   Code   RO         1832    i.ADC_DMACmd        stm32f10x_adc.o
    0x080015b8   0x080015b8   0x00000016   Code   RO         1836    i.ADC_ExternalTrigConvCmd  stm32f10x_adc.o
    0x080015ce   0x080015ce   0x00000014   Code   RO         1839    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x080015e2   0x080015e2   0x00000014   Code   RO         1845    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x080015f6   0x080015f6   0x00000002   PAD
    0x080015f8   0x080015f8   0x00000050   Code   RO         1849    i.ADC_Init          stm32f10x_adc.o
    0x08001648   0x08001648   0x000000b8   Code   RO         1853    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x08001700   0x08001700   0x0000000a   Code   RO         1854    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x0800170a   0x0800170a   0x0000000a   Code   RO         1858    i.ADC_StartCalibration  stm32f10x_adc.o
    0x08001714   0x08001714   0x00000004   Code   RO          158    i.BusFault_Handler  stm32f10x_it.o
    0x08001718   0x08001718   0x0000006c   Code   RO         2808    i.DMA1_Channel1_IRQHandler  adc.o
    0x08001784   0x08001784   0x0000001c   Code   RO         2126    i.DMA_ClearITPendingBit  stm32f10x_dma.o
    0x080017a0   0x080017a0   0x00000018   Code   RO         2127    i.DMA_Cmd           stm32f10x_dma.o
    0x080017b8   0x080017b8   0x0000014c   Code   RO         2128    i.DMA_DeInit        stm32f10x_dma.o
    0x08001904   0x08001904   0x00000012   Code   RO         2132    i.DMA_ITConfig      stm32f10x_dma.o
    0x08001916   0x08001916   0x0000003c   Code   RO         2133    i.DMA_Init          stm32f10x_dma.o
    0x08001952   0x08001952   0x00000002   Code   RO          159    i.DebugMon_Handler  stm32f10x_it.o
    0x08001954   0x08001954   0x00000116   Code   RO          465    i.GPIO_Init         stm32f10x_gpio.o
    0x08001a6a   0x08001a6a   0x00000012   Code   RO          469    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08001a7c   0x08001a7c   0x00000004   Code   RO          472    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08001a80   0x08001a80   0x00000004   Code   RO          473    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08001a84   0x08001a84   0x0000000a   Code   RO          476    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08001a8e   0x08001a8e   0x00000002   PAD
    0x08001a90   0x08001a90   0x00000130   Code   RO         2924    i.GetPowerMag       fft_calculate.o
    0x08001bc0   0x08001bc0   0x00000004   Code   RO          160    i.HardFault_Handler  stm32f10x_it.o
    0x08001bc4   0x08001bc4   0x0000005c   Code   RO         2606    i.LED_Init          led.o
    0x08001c20   0x08001c20   0x00000004   Code   RO          161    i.MemManage_Handler  stm32f10x_it.o
    0x08001c24   0x08001c24   0x00000002   Code   RO          162    i.NMI_Handler       stm32f10x_it.o
    0x08001c26   0x08001c26   0x00000002   PAD
    0x08001c28   0x08001c28   0x00000070   Code   RO          773    i.NVIC_Init         misc.o
    0x08001c98   0x08001c98   0x00000014   Code   RO          774    i.NVIC_PriorityGroupConfig  misc.o
    0x08001cac   0x08001cac   0x0000002a   Code   RO          243    i.OLED_Clear        oled.o
    0x08001cd6   0x08001cd6   0x00000002   PAD
    0x08001cd8   0x08001cd8   0x00000050   Code   RO          244    i.OLED_I2C_Init     oled.o
    0x08001d28   0x08001d28   0x00000054   Code   RO          245    i.OLED_I2C_SendByte  oled.o
    0x08001d7c   0x08001d7c   0x00000034   Code   RO          246    i.OLED_I2C_Start    oled.o
    0x08001db0   0x08001db0   0x00000028   Code   RO          247    i.OLED_I2C_Stop     oled.o
    0x08001dd8   0x08001dd8   0x000000ae   Code   RO          248    i.OLED_Init         oled.o
    0x08001e86   0x08001e86   0x00000022   Code   RO          250    i.OLED_SetCursor    oled.o
    0x08001ea8   0x08001ea8   0x00000074   Code   RO          252    i.OLED_ShowChar     oled.o
    0x08001f1c   0x08001f1c   0x00000028   Code   RO          256    i.OLED_ShowString   oled.o
    0x08001f44   0x08001f44   0x00000020   Code   RO          257    i.OLED_WriteCommand  oled.o
    0x08001f64   0x08001f64   0x00000020   Code   RO          258    i.OLED_WriteData    oled.o
    0x08001f84   0x08001f84   0x00000002   Code   RO          163    i.PendSV_Handler    stm32f10x_it.o
    0x08001f86   0x08001f86   0x00000002   PAD
    0x08001f88   0x08001f88   0x00000020   Code   RO          574    i.RCC_AHBPeriphClockCmd  stm32f10x_rcc.o
    0x08001fa8   0x08001fa8   0x00000020   Code   RO          577    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001fc8   0x08001fc8   0x000000d4   Code   RO          585    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x0800209c   0x0800209c   0x00000002   Code   RO          164    i.SVC_Handler       stm32f10x_it.o
    0x0800209e   0x0800209e   0x00000008   Code   RO         2562    i.SetSysClock       system_stm32f10x.o
    0x080020a6   0x080020a6   0x00000002   PAD
    0x080020a8   0x080020a8   0x000000e0   Code   RO         2563    i.SetSysClockTo72   system_stm32f10x.o
    0x08002188   0x08002188   0x00000028   Code   RO          777    i.SysTick_CLKSourceConfig  misc.o
    0x080021b0   0x080021b0   0x00000002   Code   RO          165    i.SysTick_Handler   stm32f10x_it.o
    0x080021b2   0x080021b2   0x00000002   PAD
    0x080021b4   0x080021b4   0x0000003c   Code   RO         2862    i.SysTick_Init      systick.o
    0x080021f0   0x080021f0   0x00000060   Code   RO         2565    i.SystemInit        system_stm32f10x.o
    0x08002250   0x08002250   0x00000090   Code   RO         2809    i.TIM1_Init         adc.o
    0x080022e0   0x080022e0   0x00000018   Code   RO          879    i.TIM_Cmd           stm32f10x_tim.o
    0x080022f8   0x080022f8   0x0000001e   Code   RO          881    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08002316   0x08002316   0x00000002   PAD
    0x08002318   0x08002318   0x00000098   Code   RO          908    i.TIM_OC1Init       stm32f10x_tim.o
    0x080023b0   0x080023b0   0x000000a4   Code   RO          950    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08002454   0x08002454   0x00000012   Code   RO          951    i.TIM_TimeBaseStructInit  stm32f10x_tim.o
    0x08002466   0x08002466   0x00000002   PAD
    0x08002468   0x08002468   0x0000003c   Code   RO         2891    i.USART1_IRQHandler  usart.o
    0x080024a4   0x080024a4   0x000000b8   Code   RO         2892    i.USART1_Init       usart.o
    0x0800255c   0x0800255c   0x00000012   Code   RO         1411    i.USART_ClearFlag   stm32f10x_usart.o
    0x0800256e   0x0800256e   0x00000018   Code   RO         1415    i.USART_Cmd         stm32f10x_usart.o
    0x08002586   0x08002586   0x0000001a   Code   RO         1418    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x080025a0   0x080025a0   0x00000054   Code   RO         1419    i.USART_GetITStatus  stm32f10x_usart.o
    0x080025f4   0x080025f4   0x0000004a   Code   RO         1421    i.USART_ITConfig    stm32f10x_usart.o
    0x0800263e   0x0800263e   0x00000002   PAD
    0x08002640   0x08002640   0x000000d8   Code   RO         1422    i.USART_Init        stm32f10x_usart.o
    0x08002718   0x08002718   0x0000000a   Code   RO         1429    i.USART_ReceiveData  stm32f10x_usart.o
    0x08002722   0x08002722   0x00000008   Code   RO         1432    i.USART_SendData    stm32f10x_usart.o
    0x0800272a   0x0800272a   0x00000004   Code   RO          166    i.UsageFault_Handler  stm32f10x_it.o
    0x0800272e   0x0800272e   0x00000002   PAD
    0x08002730   0x08002730   0x00000028   Code   RO         3197    i.__0sprintf        mc_w.l(printfa.o)
    0x08002758   0x08002758   0x00000028   Code   RO         3315    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08002780   0x08002780   0x000000aa   Code   RO         3317    i.__kernel_poly     m_ws.l(poly.o)
    0x0800282a   0x0800282a   0x00000006   Code   RO         3258    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x08002830   0x08002830   0x00000004   Code   RO         3259    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x08002834   0x08002834   0x00000010   Code   RO         3263    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08002844   0x08002844   0x0000000e   Code   RO         3329    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002852   0x08002852   0x00000002   Code   RO         3330    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002854   0x08002854   0x0000000e   Code   RO         3331    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002862   0x08002862   0x00000002   PAD
    0x08002864   0x08002864   0x0000000c   Code   RO         3295    i.__set_errno       mc_w.l(errno.o)
    0x08002870   0x08002870   0x00000184   Code   RO         3202    i._fp_digits        mc_w.l(printfa.o)
    0x080029f4   0x080029f4   0x000006dc   Code   RO         3203    i._printf_core      mc_w.l(printfa.o)
    0x080030d0   0x080030d0   0x00000024   Code   RO         3204    i._printf_post_padding  mc_w.l(printfa.o)
    0x080030f4   0x080030f4   0x0000002e   Code   RO         3205    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08003122   0x08003122   0x0000000a   Code   RO         3207    i._sputc            mc_w.l(printfa.o)
    0x0800312c   0x0800312c   0x00000220   Code   RO         3248    i.atan              m_ws.l(atan.o)
    0x0800334c   0x0800334c   0x00000180   Code   RO         2944    i.atan2             m_ws.l(atan2.o)
    0x080034cc   0x080034cc   0x00000280   Code   RO            2    i.main              main.o
    0x0800374c   0x0800374c   0x0000004c   Code   RO         2952    i.sqrt              m_ws.l(sqrt.o)
    0x08003798   0x08003798   0x000005f0   Data   RO          259    .constdata          oled.o
    0x08003d88   0x08003d88   0x00000098   Data   RO         3249    .constdata          m_ws.l(atan.o)
    0x08003e20   0x08003e20   0x00000008   Data   RO         3271    .constdata          m_ws.l(qnan.o)
    0x08003e28   0x08003e28   0x00000020   Data   RO         3327    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003e48, Size: 0x00001090, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003e48   0x00000034   Data   RW            4    .data               main.o
    0x20000034   0x08003e7c   0x00000014   Data   RW          605    .data               stm32f10x_rcc.o
    0x20000048   0x08003e90   0x00000004   Data   RW         2865    .data               systick.o
    0x2000004c   0x08003e94   0x00000004   Data   RW         2926    .data               fft_calculate.o
    0x20000050   0x08003e98   0x00000004   Data   RW         3296    .data               mc_w.l(errno.o)
    0x20000054        -       0x0000003c   Zero   RW            3    .bss                main.o
    0x20000090        -       0x00000400   Zero   RW         2810    .bss                adc.o
    0x20000490        -       0x00000800   Zero   RW         2925    .bss                fft_calculate.o
    0x20000c90        -       0x00000400   Zero   RW          451    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       502         22          0          0          0       3038   ad9833.o
       502         22          0          0          0       3098   ad9833_2.o
       588         58          0          0       1024       3402   adc.o
      1502       1008          0          0          0        288   cr4_fft_256_stm32.o
       304         30          0          4       2048       1433   fft_calculate.o
        92         10          0          0          0        467   led.o
       640        108          0         52         60     273917   main.o
       172         22          0          0          0       2125   misc.o
       726         22       1520          0          0       5999   oled.o
        36          8        304          0       1024        812   startup_stm32f10x_hd.o
       390         10          0          0          0       7536   stm32f10x_adc.o
       462         18          0          0          0       4621   stm32f10x_dma.o
       314          0          0          0          0       4320   stm32f10x_gpio.o
        26          0          0          0          0       3578   stm32f10x_it.o
       276         32          0         20          0       4497   stm32f10x_rcc.o
       388         62          0          0          0       3653   stm32f10x_tim.o
       460          6          0          0          0       6847   stm32f10x_usart.o
       328         28          0          0          0       1833   system_stm32f10x.o
        60          8          0          4          0        655   systick.o
       244         16          0          0          0       1122   usart.o

    ----------------------------------------------------------------------
      8038       <USER>       <GROUP>         80       4156     333241   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       544         70        152          0          0        124   atan.o
       384         38          0          0          0        144   atan2.o
        26          6          0          0          0        204   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o
        76          0          0          0          0         84   sqrt.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2276         86          0          0          0        520   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        46          0          0          0          0         80   dscalb.o
       162          0          0          0          0        100   dsqrt.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      5890        <USER>        <GROUP>          4          0       3508   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1240        114        160          0          0        720   m_ws.l
      2614        108          0          4          0       1032   mc_w.l
      2032          0          0          0          0       1756   mf_w.l

    ----------------------------------------------------------------------
      5890        <USER>        <GROUP>          4          0       3508   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13928       1712       2016         84       4156     330973   Grand Totals
     13928       1712       2016         84       4156     330973   ELF Image Totals
     13928       1712       2016         84          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15944 (  15.57kB)
    Total RW  Size (RW Data + ZI Data)              4240 (   4.14kB)
    Total ROM Size (Code + RO Data + RW Data)      16028 (  15.65kB)

==============================================================================

