--cpu Cortex-M3
".\obj\main.o"
".\obj\stm32f10x_it.o"
".\obj\oled.o"
".\obj\timer.o"
".\obj\ad9833.o"
".\obj\ad9833_2.o"
".\obj\startup_stm32f10x_hd.o"
".\obj\stm32f10x_gpio.o"
".\obj\stm32f10x_rcc.o"
".\obj\misc.o"
".\obj\stm32f10x_exti.o"
".\obj\stm32f10x_tim.o"
".\obj\stm32f10x_usart.o"
".\obj\stm32f10x_iwdg.o"
".\obj\stm32f10x_wwdg.o"
".\obj\stm32f10x_bkp.o"
".\obj\stm32f10x_pwr.o"
".\obj\stm32f10x_adc.o"
".\obj\stm32f10x_dac.o"
".\obj\stm32f10x_dma.o"
".\obj\stm32f10x_rtc.o"
".\obj\stm32f10x_can.o"
".\obj\stm32f10x_fsmc.o"
".\obj\system_stm32f10x.o"
".\obj\core_cm3.o"
".\obj\led.o"
".\obj\tftlcd.o"
".\obj\adc.o"
".\obj\system.o"
".\obj\systick.o"
".\obj\usart.o"
".\obj\cr4_fft_256_stm32.o"
".\obj\cr4_fft_1024_stm32.o"
".\obj\fft_calculate.o"
--library_type=microlib --strict --scatter ".\Obj\Template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Obj\Template.map" -o .\Obj\Template.axf