#include <stdio.h>
#include "string.h"
#include "adc.h"
#include "fft_calculate.h"
#include "led.h"
#include "ad9833.h"
#include "ad9833_2.h"
#define ADC1_DR_Address    ((uint32_t)0x4001244C)
extern long AFreq,BFreq,big_AFreq,big_BFreq;
uint16_t ADC_Value[ADC_LEN*2];
float ADCdata,last_ADCdata;
float INADCdata,last_INADCdata;
extern char run_flag;
int T0,T1;
char read_flag=0;
int Time;
void ADC1_Init(void)
{
    ADC_InitTypeDef ADC_InitStructure;
		GPIO_InitTypeDef GPIO_InitStructure;
		GPIO_InitTypeDef GPIOA_InitStructure;
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	GPIOA_InitStructure.GPIO_Pin = GPIO_Pin_2;
	GPIO_Init(GPIOA, &GPIOA_InitStructure);
	
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = ENABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T1_CC1;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = 2;
    ADC_Init(ADC1, &ADC_InitStructure);

 //   ADC_TempSensorVrefintCmd(ENABLE);
    ADC_RegularChannelConfig(ADC1, ADC_Channel_11, 1, ADC_SampleTime_1Cycles5);
	ADC_RegularChannelConfig(ADC1, ADC_Channel_2, 2, ADC_SampleTime_1Cycles5);
    ADC_Cmd(ADC1, ENABLE);

    ADC_ExternalTrigConvCmd(ADC1, ENABLE);

    ADC_DMACmd(ADC1, ENABLE);

    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));

    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));  
}

void ADC1_DMA1_Init(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);

    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);

    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    DMA_DeInit(DMA1_Channel1);
    DMA_InitStructure.DMA_PeripheralBaseAddr = ADC1_DR_Address;
    DMA_InitStructure.DMA_MemoryBaseAddr = (uint32_t)ADC_Value;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = ADC_LEN*2;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init(DMA1_Channel1, &DMA_InitStructure);

    DMA_ITConfig(DMA1_Channel1, DMA_IT_TC | DMA_IT_HT, ENABLE);

    DMA_Cmd(DMA1_Channel1, ENABLE);
}

void TIM1_Init(void)
{
    TIM_TimeBaseInitTypeDef   TIM_TimeBaseStructure;
    TIM_OCInitTypeDef         TIM_OCInitStructure;
		GPIO_InitTypeDef GPIO_InitStructure;

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    TIM_TimeBaseStructInit(&TIM_TimeBaseStructure); 
    TIM_TimeBaseStructure.TIM_Period = ARR;          
    TIM_TimeBaseStructure.TIM_Prescaler = PSC;       
    TIM_TimeBaseStructure.TIM_ClockDivision = 0x00;    
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;  
    TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);

    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1; 
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;                
    TIM_OCInitStructure.TIM_Pulse = 60; 
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_Low;         
    TIM_OC1Init(TIM1, &TIM_OCInitStructure);

    TIM_CtrlPWMOutputs(TIM1, ENABLE);
    TIM_Cmd(TIM1, DISABLE);
}

void DMA1_Channel1_IRQHandler(void)
{
	u16 i = 0;
	DMA_Cmd(DMA1_Channel1, DISABLE);
	DMA_ClearITPendingBit(DMA_IT_HT);
	DMA_ClearITPendingBit(DMA1_IT_TC1);
	for(i=0;i<NPT*2;i++)
	{
		InBufArray[i] = ((signed short)(ADC_Value[i*2])) << 16;		//
		
//		last_INADCdata=INADCdata;
//		INADCdata=(float) ADC_Value[i*2]/4096*3.3;
//		if(read_flag==0&&INADCdata-1.5>=0&&last_INADCdata-1.5<0)
//		{
//			T1=0;
//			TIM_Cmd(TIM2,ENABLE);//ʹ��TIMx����
//			read_flag=1;		
//		}
//		else if(read_flag==1&&INADCdata-1.5<=0&&last_INADCdata-1.5>0)
//		{
//					
//			T1 = T1+TIM2->CNT;		//��ȡ��ʱ����ֵ
//			TIM_Cmd(TIM2, DISABLE);		//�رն�ʱ��
//			TIM2->CNT = 0;			//����������
//			read_flag=2;
//		}
//		else if(read_flag==2&&INADCdata-1.5>=0&&last_INADCdata-1.5<0)
//		{
//			TIM_Cmd(TIM2,ENABLE);//ʹ��TIMx����
//			read_flag=3;
//		}
//		else if(read_flag==3&&INADCdata-1.5<=0&&last_INADCdata-1.5>0)
//		{
//			T1 = T1+TIM2->CNT;		//��ȡ��ʱ����ֵ
//			TIM_Cmd(TIM2, DISABLE);		//�رն�ʱ��
//			TIM2->CNT = 0;			//����������
//			read_flag=0;
//		}
//		
//		last_ADCdata=ADCdata;
//		ADCdata =(float) ADC_Value[i*2+1]/4096*3.3;
//		if(ADCdata-1.5<=0&&last_ADCdata-1.5>0)
//		{
//			T0=i*2+1;
//		}
//		if(T1!=0&&T0!=0)
//		{
//			read_flag=1;
//		}
//		if(read_flag==1)
//		{
//			Time=(T1-T0);
//			T1=0;
//			T0=0;
//			read_flag=0;
//			if(Time<0)
//			{
//				Time=-Time;
//				Time=Time%180;
//				Time=180-Time;
//			}	
//			else
//			{
//				Time=Time%180;
//			}
////			AD9833_WaveSeting(big_AFreq/1000*1000,0,SIN_WAVE,Time+90 );//100KHz,	Ƶ�ʼĴ���0�����Ҳ���� ,����λ0 
////			AD9833_2_WaveSeting(big_BFreq/1000*1000,0,SIN_WAVE_2,0);
////			AD9833_AmpSet(100); //���÷�ֵ����ֵ��� 255
////			AD9833_2_AmpSet(100);
//		}

	}	
	//cr4_fft_1024_stm32(OutBufArray, InBufArray, NPT);
	cr4_fft_256_stm32(OutBufArray, InBufArray, NPT);
	GetPowerMag();	
	DMA_Cmd(DMA1_Channel1, ENABLE);
	run_flag=0;
}
