{"configurations": [{"name": "Target 1", "includePath": ["d:\\EGDownload\\2023国电赛H题\\projuct\\User", "d:\\EGDownload\\2023国电赛H题\\projuct\\Libraries\\CMSIS", "d:\\EGDownload\\2023国电赛H题\\projuct\\Libraries\\STM32F10x_StdPeriph_Driver\\inc", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\led", "d:\\EGDownload\\2023国电赛H题\\projuct\\Public", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\beep", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\smg", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\key", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\exti", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\time", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\pwm", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\iwdg", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\wwdg", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\input", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\touch_key", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\wkup", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\adc", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\adc_temp", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\dac", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\pwm_dac", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\dma", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\rtc", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\24Cxx", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\iic", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\ds18b20", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\hwjs", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\rs485", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\can", "d:\\EGDownload\\2023国电赛H题\\projuct\\APP\\tftlcd", "d:\\EGDownload\\2023国电赛H题\\projuct\\DSP_Library", "d:\\EGDownload\\2023国电赛H题\\projuct\\DSP_Library\\Inc", "E:\\Keil5\\ARM\\ARMCC\\include", "E:\\Keil5\\ARM\\ARMCC\\include\\rw", "d:\\EGDownload\\2023国电赛H题\\projuct\\Libraries\\STM32F10x_StdPeriph_Driver\\src", "d:\\EGDownload\\2023国电赛H题\\projuct\\DSP_Library\\Src"], "defines": ["USE_STDPERIPH_DRIVER", "STM32F10X_HD", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}