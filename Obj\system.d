.\obj\system.o: Public\system.c
.\obj\system.o: Public\system.h
.\obj\system.o: .\User\stm32f10x.h
.\obj\system.o: .\Libraries\CMSIS\core_cm3.h
.\obj\system.o: D:\keil\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\system.o: .\Libraries\CMSIS\system_stm32f10x.h
.\obj\system.o: .\User\stm32f10x_conf.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_adc.h
.\obj\system.o: .\User\stm32f10x.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_bkp.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_can.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_cec.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_crc.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_dac.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_dbgmcu.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_dma.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_exti.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_flash.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_i2c.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_iwdg.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_pwr.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rtc.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_sdio.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_spi.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_tim.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_usart.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\stm32f10x_wwdg.h
.\obj\system.o: .\Libraries\STM32F10x_StdPeriph_Driver\inc\misc.h
