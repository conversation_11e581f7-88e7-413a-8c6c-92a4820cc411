#include "system.h"
#include "SysTick.h"
#include "led.h"
#include "usart.h"
#include "tftlcd.h"
#include "fft_calculate.h"
#include "adc.h"
#include "OLED.h"
#include "Timer.H"
#include "ad9833.h"
#include "ad9833_2.h"
#include "math.h"
long AFreq,BFreq,big_AFreq=0,big_BFreq=0;
u16 Row,last_Row;
u16 Max_Val=0, last_Max_Val=0;
u16 last_data=0,now_data=0,next_data=0;
u16 adc_value[5];
char txt[50];
char run_flag=0;
u16 Pha;
extern int P;
extern float ADCdata;
extern int Time;
extern int T0,T1;
int k;
float base = 0;	//����
float harmonic = 0;	//г��
float distortion = 0;	//ʧ���
/*******************************************************************************
* �� �� ��         : main
* ��������		   : ������
* ��    ��         : ��
* ��    ��         : ��
*******************************************************************************/
void GetDistortion(void);	//ʧ��ȼ���
int main()
{
	u16 i;

	SysTick_Init(72);
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  //�ж����ȼ����� ��2��
	OLED_Init();
	LED_Init();
	AD9833_Init_GPIO(); 	//AD9833 GPIO ��ʼ��
	AD9833_2_Init_GPIO();	//AD9833_2 ��ʼ��	
	USART1_Init(115200);
	TIM1_Init();
	ADC1_DMA1_Init();
	ADC1_Init();
	TIM_Cmd(TIM1, ENABLE);
//	TIM_Init_counter(TIM2);//��ʱ��2��ʼ��
//	TIM_Cmd(TIM2,DISABLE);//ʹ��TIMx����
	while(1)
	{
		DMA_Cmd(DMA1_Channel1, ENABLE);
		run_flag=1;
		while(run_flag==1);
//			DMA_Cmd(DMA1_Channel1, DISABLE);
		Max_Val=0;
		last_Max_Val=0;
		Row=0;
		last_Row=0;
		last_data=0;
		now_data=0;
		next_data=0;
		for(i=2;i<NPT/2;i++)
		{
	 		last_data=MagBufArray[i-1];
			now_data=MagBufArray[i];
			next_data=MagBufArray[i+1];
			if(now_data>=last_data&&now_data>=next_data)
			{
				Max_Val=now_data;	
			}
			if(Max_Val>last_Max_Val)
			{
				last_Max_Val=Max_Val;
				last_Row=Row;
				Row=i;
			}
		}
		BFreq=(72000000)/(NPT*(ARR+1)*(PSC+1)/Row);
		AFreq=(72000000)/(NPT*(ARR+1)*(PSC+1)/last_Row);
		if(BFreq>big_BFreq)
			big_BFreq=BFreq;
		if(AFreq>big_AFreq)
			big_AFreq=AFreq;
		if(BFreq-AFreq>5000)
		{
			sprintf(txt,"AFreq:%d",big_AFreq);
			OLED_ShowString(1,2,txt);
			sprintf(txt,"BFreq:%d",big_BFreq);
			OLED_ShowString(2,2,txt);	
			sprintf(txt,"k:%.2f",distortion);
			OLED_ShowString(4,2,txt);
//			GetDistortion();
//			if ((int)distortion> 12)
//			{
//				OLED_ShowString(3,2," TRI ");
//				AD9833_2_WaveSeting(big_BFreq/1000*1000,0,TRI_WAVE_2,0);
//				
//			}
//			else 
//			{
//				OLED_ShowString(3,2,"SIN");	
//				AD9833_2_WaveSeting(big_BFreq/1000*1000,0,SIN_WAVE_2,0);
//			}
			AD9833_WaveSeting(big_AFreq/1000*1000,0,SIN_WAVE,0);//100KHz,	Ƶ�ʼĴ���0�����Ҳ���� ,����λ0 
			AD9833_2_WaveSeting(big_BFreq/1000*1000,0,SIN_WAVE_2,0);
			AD9833_AmpSet(100); //���÷�ֵ����ֵ��� 255
			AD9833_2_AmpSet(100);
			
		}
		if(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_12)==0)
		{
			while(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_12)==0);
			big_AFreq=0;
			big_BFreq=0;
//			GetDistortion();
//			if ((int)distortion> 12)
//			{
//				OLED_ShowString(3,2," triangle ");	
//			}
//			else 
//			{
//				OLED_ShowString(3,2,"square");	
//			}
			//������ �������� ������г�����ȵ� ������ ʵ�ʲ�����2~4֮�両��
			//���ǲ��Ļ�������������г�����ȵ� �ű�ʵ�ʲ����� 6 ~ 12 �両��
			//���Ҳ�����������г��������0��kֵ��ǳ���
		}
	}
}
////��ʱ��2���ж�
//void TIM2_IRQHandler(void)
//{
//	if(TIM_GetITStatus(TIM2,TIM_IT_Update)==SET)
//	{
//		TIM_ClearITPendingBit(TIM2,TIM_IT_Update);

//	}	
//}


void GetDistortion(void)	//ʧ��ȼ���
{
	int i = 0;

	base = Max_Val;
	//printf("���εĻ���Ϊ %.2f\r\n", base);
	for(i = 1; i < NPT/2; i++)
	{
		harmonic += MagBufArray[i]*MagBufArray[i];
	}
	//printf("����г���ĺ� %.2f\r\n", harmonic);
	harmonic -= base*base;	//��ȥ����
	//printf("��ȥ����������г���ĺ� %.2f\r\n", harmonic);
	harmonic = sqrt(harmonic);
	//printf("sqrt������г���ĺ� %.2f\r\n", harmonic);
	distortion = harmonic / base;
	
	//printf("\r\nʧ���Ϊ�� %.2f%%\r\n", distortion*100);
	
}

